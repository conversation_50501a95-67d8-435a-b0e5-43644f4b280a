import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Base64 URL encoding without padding
function base64UrlEncode(data: Uint8Array): string {
  const base64 = btoa(String.fromCharCode(...data));
  return base64
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

// Create JWT token using Web Crypto API
async function createJWT(header: any, payload: any, secret: string): Promise<string> {
  // Encode header and payload
  const headerBytes = new TextEncoder().encode(JSON.stringify(header));
  const payloadBytes = new TextEncoder().encode(JSON.stringify(payload));

  const encodedHeader = base64UrlEncode(headerBytes);
  const encodedPayload = base64UrlEncode(payloadBytes);

  const data = `${encodedHeader}.${encodedPayload}`;

  // Create HMAC-SHA256 signature
  const key = new TextEncoder().encode(secret);
  const dataToSign = new TextEncoder().encode(data);

  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    key,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataToSign);
  const signatureBase64 = base64UrlEncode(new Uint8Array(signature));

  return `${data}.${signatureBase64}`;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  // Add test endpoint for debugging
  if (req.url.includes('test')) {
    return new Response(
      JSON.stringify({
        message: 'Twilio Access Token function is running',
        timestamp: new Date().toISOString()
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user from token
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      throw new Error('Invalid user token');
    }

    // Get user's company and Twilio credentials
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (roleError || !userRole) {
      throw new Error('User not associated with any company');
    }

    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('twilio_account_sid, twilio_api_key, twilio_api_secret, twilio_twiml_app_sid')
      .eq('id', userRole.company_id)
      .single();

    if (companyError || !company) {
      throw new Error('Company not found or Twilio credentials not configured');
    }

    const { twilio_account_sid, twilio_api_key, twilio_api_secret, twilio_twiml_app_sid } = company;

    if (!twilio_account_sid || !twilio_api_key || !twilio_api_secret || !twilio_twiml_app_sid) {
      throw new Error('Missing Twilio credentials. Please configure Account SID, API Key, API Secret, and TwiML App SID');
    }

    // Create JWT header
    const header = {
      typ: 'JWT',
      alg: 'HS256',
      cty: 'twilio-fpa;v=1'
    };

    // Create JWT payload for Voice access token
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: twilio_api_key,
      sub: twilio_account_sid,
      nbf: now,
      exp: now + 3600, // 1 hour expiration
      jti: `${twilio_api_key}-${now}`,
      grants: {
        identity: user.id,
        voice: {
          incoming: {
            allow: true
          },
          outgoing: {
            application_sid: twilio_twiml_app_sid
          }
        }
      }
    };

    console.log('Creating JWT with payload:', JSON.stringify(payload, null, 2));
    console.log('Using API Secret length:', twilio_api_secret.length);

    // Create the JWT token
    const accessToken = await createJWT(header, payload, twilio_api_secret);

    console.log('Generated access token length:', accessToken.length);
    console.log('Token parts:', accessToken.split('.').map(part => part.length));

    return new Response(
      JSON.stringify({
        accessToken,
        identity: user.id
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
      }
    );

  } catch (error) {
    console.error('Error generating access token:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Failed to generate access token' 
      }),
      {
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        },
      }
    );
  }
});
