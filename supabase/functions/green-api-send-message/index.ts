import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.190.0/http/server.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface SendMessageRequest {
  phoneNumber: string;
  message: string;
  leadId?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user } } = await supabaseClient.auth.getUser(token);

    if (!user) {
      throw new Error('Unauthorized');
    }

    const { phoneNumber, message, leadId }: SendMessageRequest = await req.json();

    // Get user's company and GreenAPI credentials
    const { data: userRole } = await supabaseClient
      .from('user_roles')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (!userRole) {
      throw new Error('User role not found');
    }

    const { data: company } = await supabaseClient
      .from('companies')
      .select('green_api_instance_id, green_api_token')
      .eq('id', userRole.company_id)
      .single();

    if (!company?.green_api_instance_id || !company?.green_api_token) {
      throw new Error('GreenAPI credentials not configured for company');
    }

    // Format phone number (remove any non-digits and ensure international format)
    const formattedPhone = phoneNumber.replace(/\D/g, '');
    const chatId = formattedPhone.startsWith('972') ? formattedPhone : `972${formattedPhone.substring(1)}`;

    // Send message via GreenAPI - Extract first 4 digits for subdomain
    const instanceSubdomain = company.green_api_instance_id.substring(0, 4);
    const greenApiUrl = `https://${instanceSubdomain}.api.greenapi.com/waInstance${company.green_api_instance_id}/sendMessage/${company.green_api_token}`;
    
    const greenApiResponse = await fetch(greenApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chatId: `${chatId}@c.us`,
        message: message,
      }),
    });

    const greenApiResult = await greenApiResponse.json();
    console.log('GreenAPI response:', greenApiResult);

    if (!greenApiResponse.ok) {
      throw new Error(`GreenAPI error: ${greenApiResult.error || 'Unknown error'}`);
    }

    // Find or create conversation
    let { data: conversation } = await supabaseClient
      .from('whatsapp_conversations')
      .select('id')
      .eq('company_id', userRole.company_id)
      .eq('phone_number', formattedPhone)
      .single();

    if (!conversation) {
      // Get lead name if leadId provided
      let contactName = formattedPhone;
      if (leadId) {
        const { data: lead } = await supabaseClient
          .from('leads')
          .select('full_name')
          .eq('id', leadId)
          .single();
        
        if (lead) {
          contactName = lead.full_name;
        }
      }

      const { data: newConversation } = await supabaseClient
        .from('whatsapp_conversations')
        .insert({
          company_id: userRole.company_id,
          lead_id: leadId || null,
          phone_number: formattedPhone,
          green_api_chat_id: `${chatId}@c.us`,
          contact_name: contactName,
          last_message: message,
          last_message_timestamp: new Date().toISOString(),
        })
        .select('id')
        .single();

      conversation = newConversation;
    } else {
      // Update conversation with last message
      await supabaseClient
        .from('whatsapp_conversations')
        .update({
          last_message: message,
          last_message_timestamp: new Date().toISOString(),
        })
        .eq('id', conversation.id);
    }

    // Store the message
    await supabaseClient
      .from('whatsapp_messages')
      .insert({
        conversation_id: conversation!.id,
        green_api_message_id: greenApiResult.idMessage,
        content: message,
        message_type: 'text',
        sender_type: 'outgoing',
        status: 'sent',
      });

    return new Response(
      JSON.stringify({ 
        success: true, 
        messageId: greenApiResult.idMessage,
        conversationId: conversation!.id 
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Error in green-api-send-message:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
};

serve(handler);