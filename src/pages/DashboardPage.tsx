import { useState, useMemo, useEffect } from 'react';
import { subDays } from 'date-fns';
import {
  Users,
  TrendingUp,
  DollarSign,
  Clock,
  Target,
  FileText,
  BarChart3,
  PieChart as PieChartIcon,
  Activity,
  Calendar,
  Plus,
  MessageSquare,
  FolderOpen
} from 'lucide-react';

import { useDashboardData, DashboardFilters } from '@/hooks/useDashboardData';
import { useOptimizedDashboard } from '@/hooks/useOptimizedDashboard';
import { useCompany } from '@/contexts/CompanyContext';
import { DashboardFilters as FiltersComponent } from '@/components/dashboard/filters/DashboardFilters';
import { KPICard } from '@/components/dashboard/KPICard';
import { BarChart } from '@/components/dashboard/charts/BarChart';
import { PieChart } from '@/components/dashboard/charts/PieChart';
import { LineChart } from '@/components/dashboard/charts/LineChart';
import { AreaChart } from '@/components/dashboard/charts/AreaChart';
import { Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';

// Helper function to safely format numbers
const safeToFixed = (value: any, decimals: number = 1): string => {
  const num = Number(value);
  return (isNaN(num) ? 0 : num).toFixed(decimals);
};

const DashboardPage = () => {
  const { currentCompany } = useCompany();
  const navigate = useNavigate();
  const [filters, setFilters] = useState<DashboardFilters>({
    dateRange: {
      from: subDays(new Date(), 365), // Show data from last year instead of 30 days
      to: new Date()
    }
  });

  // Monitor company changes
  useEffect(() => {
    console.log('🏢 Dashboard: Company changed to:', currentCompany?.name || 'none', currentCompany?.id);
  }, [currentCompany?.id]);

  // Use optimized dashboard with fallback
  const optimizedResult = useOptimizedDashboard(filters);
  const fallbackResult = useDashboardData(filters);

  // Use optimized if no error, otherwise fallback to old implementation
  const isUsingOptimized = !optimizedResult.isError;
  const { data: rawMetrics, isLoading, error } = isUsingOptimized
    ? optimizedResult
    : fallbackResult;

  // Transform optimized metrics to match the expected flat structure
  const metrics = useMemo(() => {
    if (!rawMetrics || typeof rawMetrics !== 'object') return null;

    if (isUsingOptimized) {
      // Transform OptimizedDashboardMetrics to DashboardMetrics
      const optimizedMetrics = rawMetrics as any;

      // Handle both nested structure (new) and flat structure (old) from database function
      const isNestedStructure = optimizedMetrics.leads && typeof optimizedMetrics.leads === 'object';

      if (isNestedStructure) {
        // New nested structure
        return {
          totalLeads: Number(optimizedMetrics.leads?.total || 0),
          closedLeads: Number(optimizedMetrics.leads?.closed || 0),
          closedDeals: Number(optimizedMetrics.leads?.closed || 0),
          closeRate: Number(optimizedMetrics.leads?.close_rate || 0),
          totalLeadValue: Number(optimizedMetrics.leads?.total_value || 0),
          closedDealsRevenue: Number(optimizedMetrics.leads?.closed_deals_revenue || 0),
          leadsByStatus: optimizedMetrics.leads?.by_status || [],

          totalCases: Number(optimizedMetrics.cases?.total || 0),
          activeCases: Number(optimizedMetrics.cases?.active || 0),
          completedCases: Number(optimizedMetrics.cases?.completed || 0),
          casesByType: optimizedMetrics.cases?.by_type || [],
          casesByStatus: optimizedMetrics.cases?.by_status || [],

          totalHoursWorked: Number(optimizedMetrics.time_entries?.total_hours || 0),
          billableHours: Number(optimizedMetrics.time_entries?.billable_hours || 0),
          averageHourlyRate: Number(optimizedMetrics.time_entries?.average_hourly_rate || 0),
          totalRevenue: Number(optimizedMetrics.leads?.closed_deals_revenue || 0),
          closedDealsCount: Number(optimizedMetrics.leads?.closed || 0),
          revenueByMonth: optimizedMetrics.revenue_by_month || [],

          utilizationRate: Number(optimizedMetrics.time_entries?.utilization_rate || 0),
          averageCaseDuration: Number(optimizedMetrics.averageCaseDuration || 0),
        };
      } else {
        // Old flat structure - convert directly with Number() to ensure numeric values
        return {
          totalLeads: Number(optimizedMetrics.totalLeads || 0),
          closedLeads: Number(optimizedMetrics.closedDealsCount || 0),
          closedDeals: Number(optimizedMetrics.closedDealsCount || 0),
          closeRate: Number(optimizedMetrics.closeRate || 0),
          totalLeadValue: Number(optimizedMetrics.totalLeadValue || 0),
          closedDealsRevenue: Number(optimizedMetrics.totalRevenue || 0),
          leadsByStatus: optimizedMetrics.leadsByStatus || [],

          totalCases: Number(optimizedMetrics.totalCases || 0),
          activeCases: Number(optimizedMetrics.activeCases || 0),
          completedCases: Number(optimizedMetrics.completedCases || 0),
          casesByType: optimizedMetrics.casesByType || [],
          casesByStatus: optimizedMetrics.casesByStatus || [],

          totalHoursWorked: Number(optimizedMetrics.totalHoursWorked || 0),
          billableHours: Number(optimizedMetrics.billableHours || 0),
          averageHourlyRate: Number(optimizedMetrics.averageHourlyRate || 0),
          totalRevenue: Number(optimizedMetrics.totalRevenue || 0),
          closedDealsCount: Number(optimizedMetrics.closedDealsCount || 0),
          revenueByMonth: optimizedMetrics.revenueByMonth || [],

          utilizationRate: Number(optimizedMetrics.utilizationRate || 0),
          averageCaseDuration: Number(optimizedMetrics.averageCaseDuration || 0),
        };
      }
    }

    // Return fallback metrics as-is, but ensure all numeric values are properly converted
    const fallbackMetrics = rawMetrics as any;
    return {
      totalLeads: Number(fallbackMetrics.totalLeads || 0),
      closedLeads: Number(fallbackMetrics.closedLeads || 0),
      closedDeals: Number(fallbackMetrics.closedDeals || 0),
      closeRate: Number(fallbackMetrics.closeRate || 0),
      totalLeadValue: Number(fallbackMetrics.totalLeadValue || 0),
      closedDealsRevenue: Number(fallbackMetrics.closedDealsRevenue || 0),
      leadsByStatus: fallbackMetrics.leadsByStatus || [],

      totalCases: Number(fallbackMetrics.totalCases || 0),
      activeCases: Number(fallbackMetrics.activeCases || 0),
      completedCases: Number(fallbackMetrics.completedCases || 0),
      casesByType: fallbackMetrics.casesByType || [],
      casesByStatus: fallbackMetrics.casesByStatus || [],

      totalHoursWorked: Number(fallbackMetrics.totalHoursWorked || 0),
      billableHours: Number(fallbackMetrics.billableHours || 0),
      averageHourlyRate: Number(fallbackMetrics.averageHourlyRate || 0),
      totalRevenue: Number(fallbackMetrics.totalRevenue || 0),
      closedDealsCount: Number(fallbackMetrics.closedDealsCount || 0),
      revenueByMonth: fallbackMetrics.revenueByMonth || [],

      utilizationRate: Number(fallbackMetrics.utilizationRate || 0),
      averageCaseDuration: Number(fallbackMetrics.averageCaseDuration || 0),
    };
  }, [rawMetrics, isUsingOptimized]);

  // Only log in development
  if (process.env.NODE_ENV === 'development') {
    console.log('📊 Dashboard using:', isUsingOptimized ? 'Optimized' : 'Fallback', 'hook');
  }

  // Handle case where no company is selected (e.g., after deletion)
  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-muted-foreground mb-2">לא נבחרה חברה</p>
          <p className="text-sm text-muted-foreground">אנא בחר חברה כדי לצפות בנתוני הדשבורד</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>טוען נתוני דשבורד...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600 mb-2">שגיאה בטעינת הנתונים</p>
          <p className="text-muted-foreground text-sm">{error instanceof Error ? error.message : String(error)}</p>
          <p className="text-xs text-muted-foreground mt-2">חברה: {currentCompany?.name || 'לא זמינה'}</p>
        </div>
      </div>
    );
  }

  if (!metrics || typeof metrics !== 'object') {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-muted-foreground mb-2">אין נתונים להצגה</p>
          <p className="text-xs text-muted-foreground">חברה: {currentCompany?.name || 'לא זמינה'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 bg-gradient-to-br from-background via-background to-muted/20 min-h-screen">
      {/* Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 rounded-lg -z-10" />
        <div className="p-6 rounded-lg backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-2">
                דשבורד
              </h1>
              <p className="text-muted-foreground text-lg">סקירה כללית של ביצועי המשרד</p>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={() => navigate('/office/leads')}
                className="flex items-center gap-2"
                variant="outline"
              >
                <Plus className="w-4 h-4" />
                ליד חדש
              </Button>
              <Button
                onClick={() => navigate('/cases')}
                className="flex items-center gap-2"
                variant="outline"
              >
                <FolderOpen className="w-4 h-4" />
                תיק חדש
              </Button>
              <Button
                onClick={() => navigate('/office/whatsapp')}
                className="flex items-center gap-2"
                variant="outline"
              >
                <MessageSquare className="w-4 h-4" />
                וואטסאפ
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <FiltersComponent filters={filters} onFiltersChange={setFilters} />

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard
          title="סה״כ לידים"
          value={metrics?.totalLeads || 0}
          subtitle="לידים בתקופה הנבחרת"
          icon={Users}
          className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        />

        <KPICard
          title="עסקאות סגורות"
          value={metrics?.closedDealsCount || 0}
          subtitle={`אחוז סגירה: ${safeToFixed(metrics?.closeRate, 1)}%`}
          icon={Target}
          className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        />

        <KPICard
          title="סה״כ הכנסות"
          value={`₪${Number(metrics?.totalRevenue || 0).toLocaleString('he-IL')}`}
          subtitle="מעסקאות סגורות"
          icon={DollarSign}
          className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        />

        <KPICard
          title="שעות עבודה"
          value={`${safeToFixed(metrics?.totalHoursWorked, 1)}`}
          subtitle={`${safeToFixed(metrics?.billableHours, 1)} שעות חיוב`}
          icon={Clock}
          className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        />
      </div>

      {/* Secondary KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard
          title="תיקים פעילים"
          value={metrics?.activeCases || 0}
          subtitle={`מתוך ${metrics?.totalCases || 0} תיקים`}
          icon={FileText}
          className="border-0 shadow-md hover:shadow-lg transition-all duration-300"
        />

        <KPICard
          title="שיעור ניצול"
          value={`${safeToFixed(metrics?.utilizationRate, 1)}%`}
          subtitle="שעות חיוב מסה״כ שעות"
          icon={Activity}
          className="border-0 shadow-md hover:shadow-lg transition-all duration-300"
        />

        <KPICard
          title="תעריף ממוצע"
          value={`₪${safeToFixed(metrics?.averageHourlyRate, 0)}`}
          subtitle="לשעה"
          icon={TrendingUp}
          className="border-0 shadow-md hover:shadow-lg transition-all duration-300"
        />

        <KPICard
          title="משך תיק ממוצע"
          value={`${safeToFixed(metrics?.averageCaseDuration, 0)}`}
          subtitle="ימים"
          icon={Calendar}
          className="border-0 shadow-md hover:shadow-lg transition-all duration-300"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Lead Status Distribution */}
        <PieChart
          title="התפלגות לידים לפי סטטוס"
          data={metrics?.leadsByStatus || []}
          dataKey="count"
          nameKey="status"
          height={350}
        />

        {/* Cases by Type */}
        <BarChart
          title="תיקים לפי סוג"
          data={metrics?.casesByType || []}
          dataKey="count"
          xAxisKey="type"
          color="#10b981"
          height={350}
        />
      </div>

      {/* Revenue and Hours Trend */}
      <div className="grid grid-cols-1 gap-6">
        <LineChart
          title="מגמת הכנסות ושעות עבודה"
          data={metrics?.revenueByMonth || []}
          lines={[
            { dataKey: 'revenue', color: '#3b82f6', name: 'הכנסות (₪)' },
            { dataKey: 'hours', color: '#10b981', name: 'שעות עבודה' }
          ]}
          xAxisKey="month"
          height={400}
        />
      </div>

      {/* Case Status Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PieChart
          title="התפלגות תיקים לפי סטטוס"
          data={metrics?.casesByStatus || []}
          dataKey="count"
          nameKey="status"
          colors={['#f59e0b', '#3b82f6', '#10b981', '#ef4444']}
          height={350}
        />

        {/* Lead Value Distribution */}
        <BarChart
          title="ערך לידים לפי סטטוס"
          data={metrics?.leadsByStatus || []}
          dataKey="value"
          xAxisKey="status"
          color="#8b5cf6"
          height={350}
        />
      </div>
    </div>
  );
};

export default DashboardPage;
