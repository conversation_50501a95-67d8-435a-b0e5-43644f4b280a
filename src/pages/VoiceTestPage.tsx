import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useCallManager } from '@/hooks/useCallManager';
import { useTwilioVoice } from '@/hooks/useTwilioVoice';
import { ActiveCallBar } from '@/components/leads/ActiveCallBar';
import { Phone, PhoneOff, Mic, MicOff, Settings, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

export const VoiceTestPage = () => {
  const [testPhoneNumber, setTestPhoneNumber] = useState('');
  const [testLeadName, setTestLeadName] = useState('Test Lead');

  const {
    isInitialized,
    isInitializing,
    initializationError,
    activeCall,
    isLoading,
    isMuted,
    audioDevices,
    volume,
    initializeDevice,
    makeCall,
    hangupCall,
    acceptCall,
    rejectCall,
    muteCall,
    unmuteCall,
    setSpeakerDevice,
    setMicrophoneDevice,
    setCallVolume
  } = useCallManager();

  // Get microphone permission status directly from the hook
  const {
    microphonePermission,
    requestMicrophonePermission,
    checkMicrophonePermission
  } = useTwilioVoice();

  const handleTestCall = async () => {
    if (!testPhoneNumber.trim()) {
      toast.error('Please enter a phone number');
      return;
    }

    try {
      await makeCall(testPhoneNumber, 'test-lead-id', testLeadName);
    } catch (error: any) {
      toast.error(`Failed to make call: ${error.message}`);
    }
  };

  const handleInitialize = async () => {
    try {
      await initializeDevice();
    } catch (error: any) {
      toast.error(`Failed to initialize: ${error.message}`);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Twilio Voice Test</h1>
        <Badge variant={isInitialized ? 'default' : 'secondary'}>
          {isInitialized ? 'Ready' : isInitializing ? 'Initializing...' : 'Not Ready'}
        </Badge>
      </div>

      {/* Active Call Bar */}
      {activeCall && (
        <ActiveCallBar
          activeCall={activeCall}
          onHangup={hangupCall}
          onMute={muteCall}
          onUnmute={unmuteCall}
          onAccept={acceptCall}
          onReject={rejectCall}
          isMuted={isMuted}
        />
      )}

      {/* Initialization Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Voice System Status
          </CardTitle>
          <CardDescription>
            Current status of the Twilio Voice SDK integration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Status</Label>
              <p className="text-sm text-muted-foreground">
                {isInitialized ? '✅ Ready' : isInitializing ? '⏳ Initializing...' : '❌ Not Ready'}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Audio Devices</Label>
              <p className="text-sm text-muted-foreground">
                {audioDevices.speakers.length} speakers, {audioDevices.microphones.length} microphones
              </p>
            </div>
          </div>

          {initializationError && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm text-destructive font-medium">Initialization Error:</p>
              <p className="text-sm text-destructive">{initializationError}</p>
            </div>
          )}

          {!isInitialized && !isInitializing && (
            <Button onClick={handleInitialize} className="w-full">
              Initialize Voice System
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Microphone Permission Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="w-5 h-5" />
            Microphone Permission
          </CardTitle>
          <CardDescription>
            Microphone access is required for voice calls
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {microphonePermission === 'granted' && (
                <>
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm font-medium text-green-700">Permission Granted</span>
                </>
              )}
              {microphonePermission === 'denied' && (
                <>
                  <XCircle className="w-5 h-5 text-red-500" />
                  <span className="text-sm font-medium text-red-700">Permission Denied</span>
                </>
              )}
              {microphonePermission === 'prompt' && (
                <>
                  <AlertCircle className="w-5 h-5 text-yellow-500" />
                  <span className="text-sm font-medium text-yellow-700">Permission Required</span>
                </>
              )}
              {microphonePermission === 'unknown' && (
                <>
                  <AlertCircle className="w-5 h-5 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Status Unknown</span>
                </>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={checkMicrophonePermission}
              >
                Check Status
              </Button>
              {microphonePermission !== 'granted' && (
                <Button
                  size="sm"
                  onClick={requestMicrophonePermission}
                >
                  Request Permission
                </Button>
              )}
            </div>
          </div>

          {microphonePermission === 'denied' && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Microphone access denied.</strong> To enable voice calls:
              </p>
              <ol className="text-sm text-yellow-700 mt-2 ml-4 list-decimal">
                <li>Click the microphone icon in your browser's address bar</li>
                <li>Select "Allow" for microphone access</li>
                <li>Refresh the page and try again</li>
              </ol>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Call */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="w-5 h-5" />
            Test Call
          </CardTitle>
          <CardDescription>
            Make a test call to verify the integration is working
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="+972501234567"
                value={testPhoneNumber}
                onChange={(e) => setTestPhoneNumber(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="name">Lead Name</Label>
              <Input
                id="name"
                placeholder="Test Lead"
                value={testLeadName}
                onChange={(e) => setTestLeadName(e.target.value)}
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleTestCall}
              disabled={!isInitialized || isLoading || !!activeCall}
              className="flex items-center gap-2"
            >
              <Phone className="w-4 h-4" />
              {isLoading ? 'Calling...' : 'Make Test Call'}
            </Button>

            {activeCall && (
              <Button
                variant="destructive"
                onClick={hangupCall}
                className="flex items-center gap-2"
              >
                <PhoneOff className="w-4 h-4" />
                Hang Up
              </Button>
            )}

            {activeCall && (
              <Button
                variant="outline"
                onClick={isMuted ? unmuteCall : muteCall}
                className="flex items-center gap-2"
              >
                {isMuted ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                {isMuted ? 'Unmute' : 'Mute'}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Audio Controls */}
      {isInitialized && (
        <Card>
          <CardHeader>
            <CardTitle>Audio Controls</CardTitle>
            <CardDescription>
              Configure audio devices and volume
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="speaker">Speaker Device</Label>
                <select
                  id="speaker"
                  className="w-full p-2 border rounded-md"
                  value={audioDevices.selectedSpeaker || ''}
                  onChange={(e) => setSpeakerDevice(e.target.value)}
                >
                  <option value="">Default Speaker</option>
                  {audioDevices.speakers.map((device) => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label || `Speaker ${device.deviceId.slice(0, 8)}`}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <Label htmlFor="microphone">Microphone Device</Label>
                <select
                  id="microphone"
                  className="w-full p-2 border rounded-md"
                  value={audioDevices.selectedMicrophone || ''}
                  onChange={(e) => setMicrophoneDevice(e.target.value)}
                >
                  <option value="">Default Microphone</option>
                  {audioDevices.microphones.map((device) => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <Label htmlFor="volume">Call Volume: {Math.round(volume * 100)}%</Label>
              <input
                id="volume"
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => setCallVolume(parseFloat(e.target.value))}
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Setup Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm text-muted-foreground">
            To test the Twilio Voice integration:
          </p>
          <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
            <li>Ensure your company has Twilio credentials configured in settings</li>
            <li>Click "Initialize Voice System" to connect to Twilio</li>
            <li>Enter a valid phone number (Israeli format: +972501234567)</li>
            <li>Click "Make Test Call" to initiate the call</li>
            <li>Use the call controls to mute/unmute or hang up</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
};
