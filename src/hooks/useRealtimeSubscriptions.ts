import { useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';

type SubscriptionCallback = (payload: any) => void;

interface SubscriptionConfig {
  table: string;
  event: string;
  callback: SubscriptionCallback;
}

// Global subscription manager to prevent duplicates
class RealtimeSubscriptionManager {
  private static instance: RealtimeSubscriptionManager;
  private subscriptions = new Map<string, any>();
  private callbacks = new Map<string, Set<SubscriptionCallback>>();

  static getInstance(): RealtimeSubscriptionManager {
    if (!RealtimeSubscriptionManager.instance) {
      RealtimeSubscriptionManager.instance = new RealtimeSubscriptionManager();
    }
    return RealtimeSubscriptionManager.instance;
  }

  subscribe(companyId: string, config: SubscriptionConfig): () => void {
    const key = `${companyId}-${config.table}-${config.event}`;
    
    // Add callback to the set
    if (!this.callbacks.has(key)) {
      this.callbacks.set(key, new Set());
    }
    this.callbacks.get(key)!.add(config.callback);

    // Create subscription if it doesn't exist
    if (!this.subscriptions.has(key)) {
      console.log(`🔗 Creating real-time subscription for ${config.table} (${config.event}) - Total callbacks: ${this.callbacks.get(key)?.size || 0} [Company: ${companyId}]`);

      const channel = supabase
        .channel(`${config.table}-${config.event}-${companyId}`)
        .on(
          'postgres_changes',
          {
            event: config.event as any,
            schema: 'public',
            table: config.table,
            filter: config.table === 'leads' || config.table === 'lead_answer_status'
              ? `company_id=eq.${companyId}`
              : undefined
          },
          (payload) => {
            const callbacks = this.callbacks.get(key);
            console.log(`📡 ${config.table} change received - Broadcasting to ${callbacks?.size || 0} callbacks:`, payload);
            // Call all registered callbacks
            if (callbacks) {
              callbacks.forEach(callback => callback(payload));
            }
          }
        )
        .subscribe();

      this.subscriptions.set(key, channel);
    } else {
      console.log(`♻️ Reusing existing subscription for ${config.table} (${config.event}) - Total callbacks: ${this.callbacks.get(key)?.size || 0} [Company: ${companyId}]`);
    }

    // Return unsubscribe function
    return () => {
      const callbacks = this.callbacks.get(key);
      if (callbacks) {
        callbacks.delete(config.callback);
        
        // If no more callbacks, schedule removal after a delay to prevent rapid recreation
        if (callbacks.size === 0) {
          console.log(`🔌 Scheduling removal of real-time subscription for ${config.table} (${config.event})`);

          // Use a small delay to prevent rapid subscription recreation
          setTimeout(() => {
            const currentCallbacks = this.callbacks.get(key);
            // Only remove if still no callbacks after delay
            if (!currentCallbacks || currentCallbacks.size === 0) {
              console.log(`🗑️ Removing real-time subscription for ${config.table} (${config.event})`);
              const channel = this.subscriptions.get(key);
              if (channel) {
                supabase.removeChannel(channel);
                this.subscriptions.delete(key);
              }
              this.callbacks.delete(key);
            }
          }, 100); // 100ms delay
        }
      }
    };
  }

  // Clean up all subscriptions for a company
  cleanupCompany(companyId: string) {
    const keysToRemove: string[] = [];
    
    this.subscriptions.forEach((channel, key) => {
      if (key.startsWith(companyId)) {
        console.log(`🧹 Cleaning up subscription: ${key}`);
        supabase.removeChannel(channel);
        keysToRemove.push(key);
      }
    });

    keysToRemove.forEach(key => {
      this.subscriptions.delete(key);
      this.callbacks.delete(key);
    });
  }
}

// Hook for managing real-time subscriptions
export const useRealtimeSubscriptions = () => {
  const { currentCompany } = useCompany();
  const manager = useRef(RealtimeSubscriptionManager.getInstance());
  const unsubscribeFunctions = useRef<(() => void)[]>([]);
  const currentCompanyRef = useRef(currentCompany?.id);

  // Update company ref
  useEffect(() => {
    currentCompanyRef.current = currentCompany?.id;
  }, [currentCompany?.id]);

  // Create a stable subscribe function using useCallback
  const subscribe = useCallback((table: string, event: string, callback: SubscriptionCallback) => {
    if (!currentCompanyRef.current) return () => {};

    const unsubscribe = manager.current.subscribe(currentCompanyRef.current, {
      table,
      event,
      callback
    });

    unsubscribeFunctions.current.push(unsubscribe);
    return unsubscribe;
  }, []); // No dependencies - function is stable

  // Cleanup on company change or unmount
  useEffect(() => {
    return () => {
      // Cleanup all subscriptions when component unmounts or company changes
      unsubscribeFunctions.current.forEach(unsubscribe => unsubscribe());
      unsubscribeFunctions.current = [];
    };
  }, [currentCompany?.id]);

  return { subscribe };
};

// Convenience hooks for specific tables
export const useLeadsRealtimeSubscription = (callback: SubscriptionCallback) => {
  const { subscribe } = useRealtimeSubscriptions();
  const callbackRef = useRef(callback);

  // Update callback ref without triggering re-subscription
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    // Use a stable wrapper function that calls the current callback
    const stableCallback = (payload: any) => {
      callbackRef.current(payload);
    };

    const unsubscribe = subscribe('leads', '*', stableCallback);
    return unsubscribe;
  }, [subscribe]); // Only depend on subscribe, not callback
};

export const useLeadAnswerStatusRealtimeSubscription = (callback: SubscriptionCallback) => {
  const { subscribe } = useRealtimeSubscriptions();
  const callbackRef = useRef(callback);

  // Update callback ref without triggering re-subscription
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    // Use a stable wrapper function that calls the current callback
    const stableCallback = (payload: any) => {
      callbackRef.current(payload);
    };

    const unsubscribe = subscribe('lead_answer_status', '*', stableCallback);
    return unsubscribe;
  }, [subscribe]); // Only depend on subscribe, not callback
};

export const useWhatsAppRealtimeSubscriptions = (
  conversationCallback: SubscriptionCallback,
  messageCallback: SubscriptionCallback
) => {
  const { subscribe } = useRealtimeSubscriptions();
  const conversationCallbackRef = useRef(conversationCallback);
  const messageCallbackRef = useRef(messageCallback);

  // Update callback refs without triggering re-subscription
  useEffect(() => {
    conversationCallbackRef.current = conversationCallback;
  }, [conversationCallback]);

  useEffect(() => {
    messageCallbackRef.current = messageCallback;
  }, [messageCallback]);

  useEffect(() => {
    // Use stable wrapper functions that call the current callbacks
    const stableConversationCallback = (payload: any) => {
      conversationCallbackRef.current(payload);
    };

    const stableMessageCallback = (payload: any) => {
      messageCallbackRef.current(payload);
    };

    const unsubscribeConversations = subscribe('whatsapp_conversations', '*', stableConversationCallback);
    const unsubscribeMessages = subscribe('whatsapp_messages', '*', stableMessageCallback);

    return () => {
      unsubscribeConversations();
      unsubscribeMessages();
    };
  }, [subscribe]); // Only depend on subscribe, not callbacks
};
