import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Lead } from '@/components/leads/LeadCard';
import { toast } from 'sonner';
import { useCompany } from '@/contexts/CompanyContext';
import { useAuth } from '@/hooks/useAuth';
import { useLeadsRealtimeSubscription, useLeadAnswerStatusRealtimeSubscription } from '@/hooks/useRealtimeSubscriptions';


export const useLeads = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentCompany } = useCompany();
  const { isSuperAdmin } = useAuth();

  const fetchLeads = useCallback(async () => {
    // Don't fetch if no company is selected/available
    if (!currentCompany) {
      setLeads([]);
      setIsLoading(false);
      return;
    }

    try {
      // Try using the RPC function first, fallback to direct query if it fails
      let data, error;

      try {
        const result = await supabase
          .rpc('get_leads_with_users', { company_uuid: currentCompany.id });
        data = result.data;
        error = result.error;
      } catch (rpcError) {
        console.warn('RPC function failed, falling back to direct query:', rpcError);

        // Fallback to direct table query
        const result = await supabase
          .from('leads')
          .select(`
            *,
            lead_answer_status(answer_status, attempt_number)
          `)
          .eq('company_id', currentCompany.id)
          .order('created_at', { ascending: false });

        data = result.data;
        error = result.error;
      }

      if (error) throw error;

      setLeads(data || []);
    } catch (error) {
      console.error('Error fetching leads:', error);
      toast.error('שגיאה בטעינת הלידים');
    } finally {
      setIsLoading(false);
    }
  }, [currentCompany]);

  const addLead = async (leadData: Omit<Lead, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');
      if (!currentCompany) throw new Error('No company selected');

      const { data, error } = await supabase
        .from('leads')
        .insert({
          ...leadData,
          user_id: user.id,
          company_id: currentCompany.id
        })
        .select()
        .single();

      if (error) throw error;

      setLeads(prev => [data, ...prev]);
      toast.success('ליד חדש נוסף בהצלחה');
      return data;
    } catch (error) {
      console.error('Error adding lead:', error);
      toast.error('שגיאה בהוספת הליד');
      throw error;
    }
  };

  const updateLead = async (leadId: string, updates: Partial<Lead>) => {
    try {
      // Get the current lead to track status changes
      const currentLead = leads.find(lead => lead.id === leadId);
      
      const { data, error } = await supabase
        .from('leads')
        .update(updates)
        .eq('id', leadId)
        .select()
        .single();

      if (error) throw error;

      setLeads(prev => prev.map(lead => 
        lead.id === leadId ? { ...lead, ...data } : lead
      ));

      // Log the update activity
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        // Determine activity type based on what was updated
        let activityType = 'note'; // Default to 'note' as it's a general activity type
        let description = `ליד עודכן - ${Object.keys(updates).join(', ')}`;

        // If status was changed, use status_change activity type
        if (updates.status) {
          activityType = 'status_change';
          description = `סטטוס ליד שונה ל: ${updates.status}`;
        }

        await supabase
          .from('lead_activities')
          .insert({
            lead_id: leadId,
            user_id: user.id,
            company_id: data.company_id,
            activity_type: activityType,
            description: description
          });
      }



      toast.success('הליד עודכן בהצלחה');
      return data;
    } catch (error) {
      console.error('Error updating lead:', error);
      toast.error('שגיאה בעדכון הליד');
      throw error;
    }
  };

  const deleteLead = async (leadId: string) => {
    try {
      const { error } = await supabase
        .from('leads')
        .delete()
        .eq('id', leadId);

      if (error) throw error;

      setLeads(prev => prev.filter(lead => lead.id !== leadId));
      toast.success('הליד נמחק בהצלחה');
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast.error('שגיאה במחיקת הליד');
      throw error;
    }
  };

  // Memoize the callback to prevent unnecessary re-subscriptions
  const handleRealtimeUpdate = useCallback(() => {
    fetchLeads();
  }, [fetchLeads]);

  // Set up real-time subscriptions using the centralized manager
  useLeadsRealtimeSubscription(handleRealtimeUpdate);
  useLeadAnswerStatusRealtimeSubscription(handleRealtimeUpdate);

  useEffect(() => {
    fetchLeads();
  }, [currentCompany?.id]); // Re-fetch when company changes

  const getLeadById = async (leadId: string): Promise<Lead | null> => {
    try {
      const { data, error } = await supabase
        .from('leads')
        .select('*')
        .eq('id', leadId)
        .single();

      if (error) {
        console.error('Error fetching lead:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getLeadById:', error);
      return null;
    }
  };

  return {
    leads,
    isLoading,
    addLead,
    updateLead,
    deleteLead,
    refetchLeads: fetchLeads,
    getLeadById
  };
};