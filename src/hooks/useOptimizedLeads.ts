import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';
import { useAuth } from './useAuth';
import { toast } from 'sonner';

export interface Lead {
  id: string;
  full_name: string;
  phone: string;
  email?: string;
  source?: string;
  status: string;
  value?: number;
  notes?: string;
  user_id: string;
  company_id: string;
  assigned_user_id?: string;
  created_at: string;
  updated_at: string;
}

const LEADS_PAGE_SIZE = 25;

/**
 * Optimized Leads Hook with React Query
 * Implements pagination, search, caching, and optimistic updates
 */
export const useOptimizedLeads = (searchQuery?: string) => {
  const { currentCompany } = useCompany();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Paginated leads query with search
  const leadsQuery = useInfiniteQuery({
    queryKey: ['leads', currentCompany?.id, searchQuery],
    queryFn: async ({ pageParam = 0 }) => {
      if (!currentCompany?.id) throw new Error('No company selected');

      console.log(`📞 Fetching leads page ${pageParam}${searchQuery ? ` (search: ${searchQuery})` : ''}...`);
      const startTime = performance.now();

      let query = supabase
        .from('leads')
        .select('*')
        .eq('company_id', currentCompany.id)
        .order('created_at', { ascending: false })
        .range(pageParam * LEADS_PAGE_SIZE, (pageParam + 1) * LEADS_PAGE_SIZE - 1);

      // Add search filter if provided
      if (searchQuery && searchQuery.trim()) {
        query = query.or(`full_name.ilike.%${searchQuery}%,phone.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`);
      }

      const { data, error } = await query;

      const endTime = performance.now();
      console.log(`✅ Leads page ${pageParam} fetched in ${(endTime - startTime).toFixed(2)}ms`);

      if (error) throw error;
      return data || [];
    },
    enabled: !!currentCompany?.id,
    getNextPageParam: (lastPage, pages) => 
      lastPage.length === LEADS_PAGE_SIZE ? pages.length : undefined,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  // Lead statistics query
  const statsQuery = useQuery({
    queryKey: ['lead-stats', currentCompany?.id],
    queryFn: async () => {
      if (!currentCompany?.id) throw new Error('No company selected');

      const { data, error } = await supabase
        .from('leads')
        .select('status, value')
        .eq('company_id', currentCompany.id);

      if (error) throw error;

      // Calculate statistics
      const total = data.length;
      const byStatus = data.reduce((acc, lead) => {
        acc[lead.status] = (acc[lead.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const totalValue = data.reduce((sum, lead) => sum + (lead.value || 0), 0);
      const closedDeals = data.filter(lead => lead.status === 'לקוח סגור');
      const closedValue = closedDeals.reduce((sum, lead) => sum + (lead.value || 0), 0);

      return {
        total,
        byStatus,
        totalValue,
        closedDeals: closedDeals.length,
        closedValue,
        conversionRate: total > 0 ? (closedDeals.length / total) * 100 : 0
      };
    },
    enabled: !!currentCompany?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Add lead mutation
  const addLeadMutation = useMutation({
    mutationFn: async (leadData: {
      full_name: string;
      phone: string;
      email?: string;
      source?: string;
      status?: string;
      value?: number;
      notes?: string;
      assigned_user_id?: string;
    }) => {
      if (!user || !currentCompany?.id) throw new Error('User or company not available');

      const { data, error } = await supabase
        .from('leads')
        .insert({
          ...leadData,
          status: leadData.status || 'חדש',
          user_id: user.id,
          company_id: currentCompany.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onMutate: async (newLead) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['leads', currentCompany?.id] });

      // Snapshot previous value
      const previousLeads = queryClient.getQueryData(['leads', currentCompany?.id]);

      // Optimistically update cache
      queryClient.setQueryData(['leads', currentCompany?.id], (old: any) => {
        if (!old) return old;
        
        const optimisticLead = {
          id: `temp-${Date.now()}`,
          ...newLead,
          status: newLead.status || 'חדש',
          user_id: user?.id,
          company_id: currentCompany?.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        return {
          ...old,
          pages: [
            [optimisticLead, ...old.pages[0]],
            ...old.pages.slice(1)
          ]
        };
      });

      return { previousLeads };
    },
    onError: (err, newLead, context) => {
      // Rollback on error
      if (context?.previousLeads) {
        queryClient.setQueryData(['leads', currentCompany?.id], context.previousLeads);
      }
      toast.error('שגיאה ביצירת הליד');
    },
    onSuccess: (data) => {
      toast.success('הליד נוצר בהצלחה');
    },
    onSettled: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['leads', currentCompany?.id] });
      queryClient.invalidateQueries({ queryKey: ['lead-stats', currentCompany?.id] });
    },
  });

  // Update lead mutation
  const updateLeadMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Lead> }) => {
      const { data, error } = await supabase
        .from('leads')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onMutate: async ({ id, updates }) => {
      await queryClient.cancelQueries({ queryKey: ['leads', currentCompany?.id] });

      const previousLeads = queryClient.getQueryData(['leads', currentCompany?.id]);

      // Optimistically update
      queryClient.setQueryData(['leads', currentCompany?.id], (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: Lead[]) =>
            page.map((lead: Lead) =>
              lead.id === id ? { ...lead, ...updates } : lead
            )
          )
        };
      });

      return { previousLeads };
    },
    onError: (err, variables, context) => {
      if (context?.previousLeads) {
        queryClient.setQueryData(['leads', currentCompany?.id], context.previousLeads);
      }
      toast.error('שגיאה בעדכון הליד');
    },
    onSuccess: () => {
      toast.success('הליד עודכן בהצלחה');
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['leads', currentCompany?.id] });
      queryClient.invalidateQueries({ queryKey: ['lead-stats', currentCompany?.id] });
    },
  });

  // Delete lead mutation
  const deleteLeadMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('leads')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return id;
    },
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: ['leads', currentCompany?.id] });

      const previousLeads = queryClient.getQueryData(['leads', currentCompany?.id]);

      // Optimistically remove
      queryClient.setQueryData(['leads', currentCompany?.id], (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: Lead[]) =>
            page.filter((lead: Lead) => lead.id !== id)
          )
        };
      });

      return { previousLeads };
    },
    onError: (err, id, context) => {
      if (context?.previousLeads) {
        queryClient.setQueryData(['leads', currentCompany?.id], context.previousLeads);
      }
      toast.error('שגיאה במחיקת הליד');
    },
    onSuccess: () => {
      toast.success('הליד נמחק בהצלחה');
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['leads', currentCompany?.id] });
      queryClient.invalidateQueries({ queryKey: ['lead-stats', currentCompany?.id] });
    },
  });

  // Flatten paginated data
  const leads = leadsQuery.data?.pages.flat() || [];
  const stats = statsQuery.data;

  // Get lead by ID (from cache)
  const getLeadById = (id: string): Lead | undefined => {
    return leads.find(lead => lead.id === id);
  };

  // Prefetch next page
  const prefetchNextPage = () => {
    if (leadsQuery.hasNextPage && !leadsQuery.isFetchingNextPage) {
      leadsQuery.fetchNextPage();
    }
  };

  return {
    // Data
    leads,
    stats,
    
    // Loading states
    isLoading: leadsQuery.isLoading || statsQuery.isLoading,
    isFetching: leadsQuery.isFetching || statsQuery.isFetching,
    isLoadingMore: leadsQuery.isFetchingNextPage,
    
    // Pagination
    hasNextPage: leadsQuery.hasNextPage,
    fetchNextPage: leadsQuery.fetchNextPage,
    prefetchNextPage,
    
    // Mutations
    addLead: addLeadMutation.mutate,
    updateLead: updateLeadMutation.mutate,
    deleteLead: deleteLeadMutation.mutate,
    
    // Mutation states
    isAddingLead: addLeadMutation.isPending,
    isUpdatingLead: updateLeadMutation.isPending,
    isDeletingLead: deleteLeadMutation.isPending,
    
    // Utilities
    getLeadById,
    refetch: () => {
      leadsQuery.refetch();
      statsQuery.refetch();
    },
    
    // Error states
    error: leadsQuery.error || statsQuery.error,
    isError: leadsQuery.isError || statsQuery.isError,
  };
};
