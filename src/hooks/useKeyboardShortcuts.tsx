import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import React from 'react';

export const useKeyboardShortcuts = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only trigger shortcuts when not in input fields or when text is selected
      const target = event.target as HTMLElement;
      const isInInputField =
        target instanceof HTMLInputElement ||
        target instanceof HTMLTextAreaElement ||
        target instanceof HTMLSelectElement ||
        target?.contentEditable === 'true' ||
        target?.closest('input') ||
        target?.closest('textarea') ||
        target?.closest('[contenteditable="true"]');

      // Don't interfere with native copy/paste operations
      if (isInInputField) {
        return;
      }

      // Don't interfere with text selection copy operations
      const selection = window.getSelection();
      if (selection && selection.toString().length > 0) {
        return;
      }

      // Check for Ctrl/Cmd + key combinations
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'd':
            event.preventDefault();
            navigate('/dashboard');
            break;
          case 'l':
            event.preventDefault();
            navigate('/office/leads');
            break;
          case 'c':
            // Don't interfere with copy operations
            // Check if user is trying to copy text or if focus is on a copyable element
            const isCopyOperation =
              (selection && selection.toString().length > 0) ||
              target?.closest('input[readonly]') ||
              target?.closest('code') ||
              target?.tagName === 'INPUT' ||
              target?.tagName === 'TEXTAREA';

            if (!isCopyOperation) {
              event.preventDefault();
              navigate('/cases');
            }
            break;
          case 'w':
            event.preventDefault();
            navigate('/office/whatsapp');
            break;

          case 's':
            event.preventDefault();
            navigate('/settings');
            break;
        }
      }

      // Single key shortcuts (when not in input)
      switch (event.key) {
        case '1':
          navigate('/dashboard');
          break;
        case '2':
          navigate('/office/leads');
          break;
        case '3':
          navigate('/cases');
          break;
        case '4':
          navigate('/office/whatsapp');
          break;

      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [navigate]);
};

// Keyboard shortcuts help component
export const KeyboardShortcutsHelp = () => {
  return (
    <div className="text-xs text-muted-foreground space-y-1">
      <div className="font-medium mb-2">קיצורי מקלדת:</div>
      <div>Ctrl+D - דשבורד</div>
      <div>Ctrl+L - לידים</div>
      <div>Ctrl+C - תיקים</div>
      <div>Ctrl+W - וואטסאפ</div>
      <div>Ctrl+A - פגישות</div>
      <div>Ctrl+S - הגדרות</div>
      <div className="mt-2">
        <div>1-5 - ניווט מהיר</div>
      </div>
    </div>
  );
};
