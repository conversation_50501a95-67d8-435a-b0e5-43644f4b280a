import { useState, useEffect, useCallback, useRef } from 'react';
import { Device, Call } from '@twilio/voice-sdk';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { formatPhoneToE164 } from '@/utils/phoneUtils';

export interface VoiceCall {
  call: Call;
  leadId?: string;
  leadName?: string;
  phoneNumber?: string;
  startTime: Date;
  status: 'connecting' | 'connected' | 'disconnected' | 'failed';
  direction: 'outbound' | 'inbound';
}

export interface AudioDevices {
  speakers: MediaDeviceInfo[];
  microphones: MediaDeviceInfo[];
  selectedSpeaker?: string;
  selectedMicrophone?: string;
}

interface UseTwilioVoiceProps {
  autoInitialize?: boolean;
}

export const useTwilioVoice = (autoInitialize: boolean = true) => {
  const [device, setDevice] = useState<Device | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [activeCall, setActiveCall] = useState<VoiceCall | null>(null);
  const [audioDevices, setAudioDevices] = useState<AudioDevices>({
    speakers: [],
    microphones: []
  });
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(1.0);
  const [microphonePermission, setMicrophonePermission] = useState<'granted' | 'denied' | 'prompt' | 'unknown'>('unknown');

  const deviceRef = useRef<Device | null>(null);

  // Check microphone permission status
  const checkMicrophonePermission = useCallback(async () => {
    if (!navigator.permissions || !navigator.permissions.query) {
      console.log('Permissions API not supported');
      return 'unknown';
    }

    try {
      const result = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      // Only log if permission is denied to reduce console noise
      if (result.state === 'denied') {
        console.log('Microphone permission status:', result.state);
      }
      setMicrophonePermission(result.state as any);
      return result.state;
    } catch (error) {
      // Reduce console noise for permission check errors
      console.warn('Error checking microphone permission:', error.message);
      return 'unknown';
    }
  }, []);

  // Initialize Twilio Device
  const initializeDevice = useCallback(async () => {
    if (isInitializing || isInitialized || device) return;

    setIsInitializing(true);
    setInitializationError(null);

    try {
      // Get current user session
      const { data: { session } } = await supabase.auth.getSession();
      console.log('Current session:', session ? 'authenticated' : 'not authenticated');

      if (!session) {
        throw new Error('User not authenticated');
      }

      // Get access token from our Supabase function
      console.log('Calling twilio-access-token function...');
      const { data, error } = await supabase.functions.invoke('twilio-access-token', {
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error) {
        console.error('Error from twilio-access-token function:', error);
        throw new Error(`Failed to get access token: ${error.message}`);
      }

      if (!data?.accessToken) {
        console.error('No access token in response:', data);
        throw new Error('No access token received');
      }

      console.log('Access token received, checking microphone support...');
      console.log('Current protocol:', window.location.protocol);
      console.log('Current host:', window.location.host);

      // Check if we're in a secure context
      const isSecureContext = window.isSecureContext || window.location.protocol === 'https:' || window.location.hostname === 'localhost';
      console.log('Secure context:', isSecureContext);

      // Check if getUserMedia is supported but don't request permissions during initialization
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.warn('getUserMedia not supported');
        if (!isSecureContext) {
          console.warn('Not in secure context - microphone access requires HTTPS');
        }
      }

      // Check permission status without requesting access
      await checkMicrophonePermission();

      console.log('Creating Twilio Device...');

      // Create Twilio Device with optimized settings
      const twilioDevice = new Device(data.accessToken, {
        logLevel: 'info',
        codecPreferences: ['opus', 'pcmu'],
        fakeLocalDTMF: true,
        enableRingingState: true,
        allowIncomingWhileBusy: true,
        // Don't request microphone access during initialization
        edge: ['sydney', 'dublin', 'tokyo'],
        // Allow device to work without immediate microphone access
        sounds: {
          disconnect: false,
          incoming: false,
          outgoing: false
        }
      });

      // Setup device event listeners
      twilioDevice.on('ready', () => {
        console.log('Twilio Device is ready - setting isInitialized to true');
        setIsInitialized(true);
        setIsInitializing(false);
        toast.success('Voice system initialized');
      });

      // Also listen for 'registered' event as backup
      twilioDevice.on('registered', () => {
        console.log('Twilio Device registered - setting isInitialized to true');
        setIsInitialized(true);
        setIsInitializing(false);
        toast.success('Voice system initialized');
      });

      twilioDevice.on('error', (error) => {
        console.error('Twilio Device error:', error);

        // Handle specific error types
        if (error.code === 31402 || error.message?.includes('AcquisitionFailedError')) {
          console.warn('Microphone access error during initialization - this is expected, will be handled during call');
          // Don't treat microphone errors as fatal during initialization
          return;
        }

        setInitializationError(error.message);
        toast.error(`Voice system error: ${error.message}`);
      });

      twilioDevice.on('incoming', (call) => {
        console.log('Incoming call received:', call);
        handleIncomingCall(call);
      });

      twilioDevice.on('tokenWillExpire', async () => {
        console.log('Token will expire, refreshing...');
        await refreshToken();
      });

      // Debug: Log all events
      const originalEmit = twilioDevice.emit;
      twilioDevice.emit = function(event, ...args) {
        console.log('Twilio Device Event:', event, args);
        return originalEmit.call(this, event, ...args);
      };

      // Register the device
      await twilioDevice.register();
      console.log('Device registration completed');

      setDevice(twilioDevice);
      deviceRef.current = twilioDevice;

      // Manual check if device is ready after registration
      setTimeout(() => {
        if (twilioDevice.state === 'ready' || twilioDevice.state === 'registered') {
          console.log('Device state after registration:', twilioDevice.state);
          setIsInitialized(true);
          setIsInitializing(false);
          toast.success('Voice system initialized');
        }
      }, 1000);

      // Get available audio devices (non-blocking)
      updateAudioDevices().catch(error => {
        console.warn('Audio device enumeration failed, but voice calls will still work:', error);
      });

    } catch (error: any) {
      console.error('Failed to initialize Twilio Device:', error);
      setInitializationError(error.message);
      toast.error(`Failed to initialize voice system: ${error.message}`);
    } finally {
      setIsInitializing(false);
    }
  }, []);

  // Refresh access token
  const refreshToken = useCallback(async () => {
    if (!device) return;

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('No session');

      const { data, error } = await supabase.functions.invoke('twilio-access-token', {
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error || !data?.accessToken) {
        throw new Error('Failed to refresh token');
      }

      device.updateToken(data.accessToken);
      console.log('Token refreshed successfully');
    } catch (error: any) {
      console.error('Failed to refresh token:', error);
      toast.error('Failed to refresh authentication');
    }
  }, [device]);

  // Handle incoming call
  const handleIncomingCall = useCallback((call: Call) => {
    const callInfo: VoiceCall = {
      call,
      startTime: new Date(),
      status: 'connecting',
      direction: 'inbound'
    };

    setActiveCall(callInfo);
    setupCallEventListeners(call, callInfo);

    toast.info('Incoming call', {
      duration: 10000,
      action: {
        label: 'Answer',
        onClick: () => acceptCall()
      }
    });
  }, []);

  // Setup call event listeners
  const setupCallEventListeners = useCallback((call: Call, callInfo: VoiceCall) => {
    call.on('accept', () => {
      console.log('Call accepted');
      setActiveCall(prev => prev ? { ...prev, status: 'connected' } : null);
    });

    call.on('disconnect', () => {
      console.log('Call disconnected');
      setActiveCall(null);
      setIsMuted(false);
    });

    call.on('cancel', () => {
      console.log('Call cancelled');
      setActiveCall(null);
    });

    call.on('reject', () => {
      console.log('Call rejected');
      setActiveCall(null);
    });

    call.on('error', (error) => {
      console.error('Call error:', error);
      setActiveCall(prev => prev ? { ...prev, status: 'failed' } : null);
      toast.error(`Call error: ${error.message}`);
    });
  }, []);

  // Request microphone permission
  const requestMicrophonePermission = useCallback(async (): Promise<boolean> => {
    console.log('Requesting microphone permission...');

    // Check if we're in a secure context
    const isSecureContext = window.isSecureContext || window.location.protocol === 'https:' || window.location.hostname === 'localhost';

    if (!isSecureContext) {
      console.warn('Not in secure context - microphone access requires HTTPS');
      toast.error('Microphone access requires HTTPS. Please use a secure connection.');
      return false;
    }

    // Check if getUserMedia is supported
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.warn('getUserMedia not supported in this browser');
      toast.error('Microphone access not supported in this browser.');
      return false;
    }

    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      console.log('Microphone permission granted');

      // Stop the stream immediately as we just needed permission
      stream.getTracks().forEach(track => track.stop());

      // Update permission status
      setMicrophonePermission('granted');

      toast.success('Microphone access granted');
      return true;

    } catch (error: any) {
      console.error('Microphone permission denied:', error);

      // Update permission status
      setMicrophonePermission('denied');

      if (error.name === 'NotAllowedError') {
        toast.error('Microphone access denied. Please allow microphone access in your browser settings and try again.');
      } else if (error.name === 'NotFoundError') {
        toast.error('No microphone found. Please connect a microphone and try again.');
      } else if (error.name === 'NotReadableError') {
        toast.error('Microphone is being used by another application. Please close other applications and try again.');
      } else {
        toast.error(`Microphone access failed: ${error.message}`);
      }

      return false;
    }
  }, []);

  // Make outbound call
  const makeCall = useCallback(async (phoneNumber: string, leadId?: string, leadName?: string) => {
    console.log('makeCall called with state:', {
      hasDevice: !!device,
      isInitialized,
      isInitializing,
      initializationError,
      originalPhone: phoneNumber
    });

    // Lazy-initialize the Twilio Device on first call attempt
    if (!device || !isInitialized) {
      console.log('Voice system not ready. Attempting on-demand initialization...');
      try {
        await initializeDevice();
      } catch (e) {
        console.error('initializeDevice threw:', e);
      }

      // Wait up to 10s for device to become ready/registered
      const waitForReady = async (timeoutMs = 10000) => {
        const start = Date.now();
        while (Date.now() - start < timeoutMs) {
          const current = deviceRef.current as Device | null;
          if (
            (current && (current.state === 'ready' || current.state === 'registered')) ||
            isInitialized
          ) {
            return true;
          }
          await new Promise(r => setTimeout(r, 200));
        }
        return false;
      };

      const ready = await waitForReady();
      if (!ready) {
        toast.error('Voice system failed to initialize. Please try again.');
        return;
      }
    }

    try {
      // Format phone number to E.164 format for Twilio
      const formattedPhone = formatPhoneToE164(phoneNumber);
      console.log(`Phone number formatted: ${phoneNumber} -> ${formattedPhone}`);

      // Request microphone permission before making the call
      const hasPermission = await requestMicrophonePermission();
      if (!hasPermission) {
        console.log('Microphone permission denied, aborting call');
        return;
      }

      const params = {
        To: formattedPhone,
        ...(leadId && { leadId })
      };

      console.log('Connecting call with params:', params);
      const dev = (deviceRef.current as Device | null) || device;
      if (!dev) {
        toast.error('Voice device is not available');
        return;
      }
      const call = await dev.connect({ params });

      const callInfo: VoiceCall = {
        call,
        leadId,
        leadName,
        phoneNumber: formattedPhone,
        startTime: new Date(),
        status: 'connecting',
        direction: 'outbound'
      };

      setActiveCall(callInfo);
      setupCallEventListeners(call, callInfo);

      toast.success(`Calling ${leadName || formattedPhone}...`);

    } catch (error: any) {
      console.error('Failed to make call:', error);

      // Handle specific error types
      if (error.code === 31402 || error.message?.includes('AcquisitionFailedError')) {
        toast.error('Microphone access denied. Please allow microphone access in your browser and try again.');
      } else if (error.message?.includes('media') || error.message?.includes('microphone')) {
        toast.error('Microphone access failed. Please check your browser permissions and try again.');
      } else if (error.message?.includes('network') || error.message?.includes('connection')) {
        toast.error('Network connection failed. Please check your internet connection and try again.');
      } else {
        toast.error(`Failed to make call: ${error.message}`);
      }
    }
  }, [device, isInitialized, setupCallEventListeners, requestMicrophonePermission, initializeDevice]);

  // Hangup call
  const hangupCall = useCallback(() => {
    if (activeCall?.call) {
      activeCall.call.disconnect();
    }
  }, [activeCall]);

  // Accept incoming call
  const acceptCall = useCallback(() => {
    if (activeCall?.call && activeCall.direction === 'inbound') {
      activeCall.call.accept();
    }
  }, [activeCall]);

  // Reject incoming call
  const rejectCall = useCallback(() => {
    if (activeCall?.call && activeCall.direction === 'inbound') {
      activeCall.call.reject();
    }
  }, [activeCall]);

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (activeCall?.call) {
      if (isMuted) {
        activeCall.call.mute(false);
        setIsMuted(false);
      } else {
        activeCall.call.mute(true);
        setIsMuted(true);
      }
    }
  }, [activeCall, isMuted]);

  // Update audio devices
  const updateAudioDevices = useCallback(async () => {
    try {
      // Check if mediaDevices API is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        console.warn('MediaDevices API not available');
        setAudioDevices({ speakers: [], microphones: [] });
        return;
      }

      const devices = await navigator.mediaDevices.enumerateDevices();
      const speakers = devices.filter(device => device.kind === 'audiooutput');
      const microphones = devices.filter(device => device.kind === 'audioinput');

      setAudioDevices({ speakers, microphones });
    } catch (error) {
      console.error('Failed to enumerate audio devices:', error);
      // Set empty arrays as fallback
      setAudioDevices({ speakers: [], microphones: [] });
    }
  }, []);

  // Set speaker device
  const setSpeakerDevice = useCallback(async (deviceId: string) => {
    if (device && device.audio) {
      try {
        await device.audio.speakerDevices.set(deviceId);
        setAudioDevices(prev => ({ ...prev, selectedSpeaker: deviceId }));
      } catch (error) {
        console.error('Failed to set speaker device:', error);
      }
    }
  }, [device]);

  // Set microphone device
  const setMicrophoneDevice = useCallback(async (deviceId: string) => {
    if (device && device.audio) {
      try {
        await device.audio.setInputDevice(deviceId);
        setAudioDevices(prev => ({ ...prev, selectedMicrophone: deviceId }));
      } catch (error) {
        console.error('Failed to set microphone device:', error);
      }
    }
  }, [device]);

  // Set call volume
  const setCallVolume = useCallback((newVolume: number) => {
    if (device && device.audio) {
      device.audio.outgoing(newVolume);
      setVolume(newVolume);
    }
  }, [device]);

  // Check microphone permission on mount
  useEffect(() => {
    checkMicrophonePermission();
  }, [checkMicrophonePermission]);

  // Auto-initialize if requested
  useEffect(() => {
    console.log('Auto-initialize check:', {
      autoInitialize,
      isInitialized,
      isInitializing,
      initializationError,
      hasDevice: !!device
    });

    if (autoInitialize && !isInitialized && !isInitializing && !initializationError && !device) {
      console.log('Starting auto-initialization...');
      initializeDevice();
    }
  }, [autoInitialize, isInitialized, isInitializing, initializationError, device]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (deviceRef.current) {
        deviceRef.current.destroy();
      }
    };
  }, []);

  return {
    // State
    device,
    isInitialized,
    isInitializing,
    initializationError,
    activeCall,
    audioDevices,
    isMuted,
    volume,
    microphonePermission,

    // Actions
    initializeDevice,
    makeCall,
    hangupCall,
    acceptCall,
    rejectCall,
    toggleMute,
    setSpeakerDevice,
    setMicrophoneDevice,
    setCallVolume,
    updateAudioDevices,
    requestMicrophonePermission,
    checkMicrophonePermission
  };
};
