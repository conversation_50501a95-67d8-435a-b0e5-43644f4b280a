import { useState } from 'react';
import { useTwilioVoice, VoiceCall } from './useTwilioVoice';

export interface ActiveCall {
  leadId?: string;
  leadName?: string;
  phoneNumber?: string;
  startTime: Date;
  status: 'connecting' | 'connected' | 'disconnected' | 'failed';
  direction: 'outbound' | 'inbound';
}

export const useCallManager = () => {
  const [isLoading, setIsLoading] = useState(false);

  const {
    device,
    isInitialized,
    isInitializing,
    initializationError,
    activeCall: voiceCall,
    makeCall: voiceMakeCall,
    hangupCall: voiceHangup,
    acceptCall,
    rejectCall,
    toggleMute,
    isMuted,
    initializeDevice,
    audioDevices,
    volume,
    setSpeakerDevice,
    setMicrophoneDevice,
    setCallVolume,
    updateAudioDevices
  } = useTwilioVoice(false); // Do not auto-initialize by default; initialize from Voice pages explicitly

  // Convert VoiceCall to ActiveCall
  const activeCall: ActiveCall | null = voiceCall ? {
    leadId: voiceCall.leadId,
    leadName: voiceCall.leadName,
    phoneNumber: voiceCall.phoneNumber,
    startTime: voiceCall.startTime,
    status: voiceCall.status,
    direction: voiceCall.direction
  } : null;

  const initiateCall = async (leadId: string, leadName: string, phoneNumber: string) => {
    setIsLoading(true);
    try {
      await voiceMakeCall(phoneNumber, leadId, leadName);
    } finally {
      setIsLoading(false);
    }
  };

  const makeCall = async (phoneNumber: string, leadId: string, leadName: string) => {
    return initiateCall(leadId, leadName, phoneNumber);
  };

  const hangupCall = () => {
    voiceHangup();
  };

  const muteCall = () => {
    if (!isMuted) {
      toggleMute();
    }
  };

  const unmuteCall = () => {
    if (isMuted) {
      toggleMute();
    }
  };

  return {
    // State
    device,
    isInitialized,
    isInitializing,
    initializationError,
    activeCall,
    isLoading,
    audioDevices,
    isMuted,
    volume,
    voiceCall, // For compatibility

    // Actions
    initializeDevice,
    makeCall,
    initiateCall,
    hangupCall,
    muteCall,
    unmuteCall,
    acceptCall,
    rejectCall,
    toggleMute,
    setSpeakerDevice,
    setMicrophoneDevice,
    setCallVolume,
    updateAudioDevices
  };
};
